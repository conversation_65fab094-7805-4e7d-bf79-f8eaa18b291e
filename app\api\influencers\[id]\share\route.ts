import { NextRequest, NextResponse } from 'next/server';
import { withUserIsolation } from '@/lib/middleware/user-isolation';
import { db } from '@/lib/firebase-admin';
import { gql } from '@apollo/client';
import { v4 as uuidv4 } from 'uuid';

interface RouteParams {
  params: Promise<{
    id: string;
  }>;
}

// Query GraphQL para buscar dados básicos do influenciador
const GET_INFLUENCER_BASIC_DATA_QUERY = `
  query GetInfluencerBasicData($id: ID!, $userId: ID!) {
    influencer(id: $id, userId: $userId) {
      id
      name
      userId
      isVerified
      location
      age
      gender
      bio
      avatar
      backgroundImage
      totalFollowers
      engagementRate
      mainPlatform
      categories
      createdAt
      updatedAt
    }
  }
`;

/**
 * POST /api/influencers/[id]/share - Criar token de compartilhamento para perfil de influenciador
 */
export async function POST(req: NextRequest, { params }: RouteParams) {
  return withUserIsolation(
    async (req: NextRequest, userId: string, context: any) => {
      const resolvedParams = await params;
      const { id: influencerId } = resolvedParams;

      try {
        console.log('[API_INFLUENCER_SHARE] Iniciando compartilhamento:', {
          influencerId,
          userId
        });

        // 1. Verificar se o influenciador existe e o usuário tem acesso
        const graphqlUrl = `${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/api/graphql`;

        const response = await fetch(graphqlUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            query: GET_INFLUENCER_BASIC_DATA_QUERY,
            variables: {
              id: influencerId,
              userId: userId
            }
          })
        });

        if (!response.ok) {
          throw new Error(`GraphQL request failed: ${response.status}`);
        }

        const { data, errors } = await response.json();

        if (errors && errors.length > 0) {
          throw new Error(`GraphQL errors: ${errors.map((e: any) => e.message).join(', ')}`);
        }

        if (!data?.influencer) {
          return NextResponse.json(
            { error: 'Influenciador não encontrado' },
            { status: 404 }
          );
        }

        const influencer = data.influencer;

        // 2. Verificar se o usuário tem permissão para compartilhar este influenciador
        // (apenas o criador ou admin pode compartilhar)
        if (influencer.userId !== userId) {
          return NextResponse.json(
            { error: 'Sem permissão para compartilhar este influenciador' },
            { status: 403 }
          );
        }

        console.log('[API_INFLUENCER_SHARE] Influenciador encontrado:', {
          id: influencer.id,
          name: influencer.name,
          owner: influencer.userId
        });

        // 3. Gerar token seguro
        const shareToken = uuidv4().replace(/-/g, '');
        
        // 4. Calcular data de expiração (30 dias para perfis de influenciadores)
        const expiresAt = new Date();
        expiresAt.setDate(expiresAt.getDate() + 30);

        // 5. Salvar token no banco com estrutura para influenciador
        const shareData = {
          token: shareToken,
          type: 'influencer_profile', // Tipo específico para perfis
          influencerId: influencerId,
          influencerName: influencer.name,
          influencerData: {
            id: influencer.id,
            name: influencer.name,
            verified: influencer.isVerified,
            location: influencer.location,
            mainPlatform: influencer.mainPlatform,
            totalFollowers: influencer.totalFollowers,
            categories: influencer.categories
          },
          createdBy: userId,
          createdAt: new Date(),
          expiresAt: expiresAt,
          isActive: true,
          accessCount: 0,
          lastAccessed: null,
          // Metadados para auditoria
          shareType: 'public_profile'
        };

        await db.collection('share_tokens').doc(shareToken).set(shareData);

        console.log('[API_INFLUENCER_SHARE] Token criado:', {
          token: shareToken,
          expiresAt,
          influencerId
        });

        // 6. Atualizar estatísticas do influenciador (opcional)
        try {
          const influencerRef = db.collection('influencers').doc(influencerId);
          const influencerDoc = await influencerRef.get();
          
          if (influencerDoc.exists) {
            const currentStats = influencerDoc.data()?.statistics || {};
            await influencerRef.update({
              'statistics.totalShares': (currentStats.totalShares || 0) + 1,
              'statistics.lastSharedAt': new Date(),
              updatedAt: new Date()
            });
          }
        } catch (updateError) {
          console.warn('[API_INFLUENCER_SHARE] Erro ao atualizar estatísticas:', updateError);
          // Não falhar a operação por causa disso
        }

        // 7. Gerar URL de compartilhamento limpa para /shared/influencer/[token]
        const baseUrl = process.env.NEXT_PUBLIC_APP_URL ||
                       (req.headers.get('host') ? `https://${req.headers.get('host')}` : 'http://localhost:3000');

        const shareUrl = `${baseUrl}/shared/influencer/${shareToken}`;

        return NextResponse.json({
          success: true,
          shareUrl,
          token: shareToken,
          expiresAt: expiresAt.toISOString(),
          influencerName: influencer.name,
          influencerId: influencer.id
        });

      } catch (error: any) {
        console.error('[API_INFLUENCER_SHARE] Erro:', {
          influencerId,
          userId,
          error: error.message,
          stack: error.stack
        });

        if (error?.message?.includes('não encontrado')) {
          return NextResponse.json(
            { error: 'Influenciador não encontrado' },
            { status: 404 }
          );
        }

        if (error?.message?.includes('permissão')) {
          return NextResponse.json(
            { error: 'Sem permissão para compartilhar este influenciador' },
            { status: 403 }
          );
        }

        return NextResponse.json(
          { error: 'Erro interno ao criar compartilhamento' },
          { status: 500 }
        );
      }
    }
  )(req);
}

/**
 * GET /api/influencers/[id]/share - Listar compartilhamentos ativos do influenciador
 */
export async function GET(req: NextRequest, { params }: RouteParams) {
  return withUserIsolation(
    async (req: NextRequest, userId: string, context: any) => {
      const resolvedParams = await params;
      const { id: influencerId } = resolvedParams;

      try {
        // Verificar acesso ao influenciador
        const graphqlUrl = `${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/api/graphql`;

        const response = await fetch(graphqlUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            query: GET_INFLUENCER_BASIC_DATA_QUERY,
            variables: {
              id: influencerId,
              userId: userId
            }
          })
        });

        if (!response.ok) {
          throw new Error(`GraphQL request failed: ${response.status}`);
        }

        const { data, errors } = await response.json();

        if (errors && errors.length > 0) {
          throw new Error(`GraphQL errors: ${errors.map((e: any) => e.message).join(', ')}`);
        }

        if (!data?.influencer) {
          return NextResponse.json(
            { error: 'Influenciador não encontrado' },
            { status: 404 }
          );
        }

        // Buscar tokens ativos para este influenciador
        const tokensSnapshot = await db.collection('share_tokens')
          .where('influencerId', '==', influencerId)
          .where('createdBy', '==', userId)
          .where('isActive', '==', true)
          .where('expiresAt', '>', new Date())
          .orderBy('expiresAt', 'desc')
          .orderBy('createdAt', 'desc')
          .get();

        const activeShares = tokensSnapshot.docs.map(doc => {
          const data = doc.data();
          return {
            token: data.token,
            createdAt: data.createdAt.toDate(),
            expiresAt: data.expiresAt.toDate(),
            accessCount: data.accessCount || 0,
            lastAccessed: data.lastAccessed?.toDate() || null,
            influencerName: data.influencerName
          };
        });

        return NextResponse.json({
          success: true,
          shares: activeShares,
          total: activeShares.length
        });

      } catch (error: any) {
        console.error('[API_INFLUENCER_SHARE_GET] Erro:', {
          influencerId,
          userId,
          error: error.message
        });

        return NextResponse.json(
          { error: 'Erro interno ao buscar compartilhamentos' },
          { status: 500 }
        );
      }
    }
  )(req);
}

/**
 * DELETE /api/influencers/[id]/share - Revogar todos os compartilhamentos do influenciador
 */
export async function DELETE(req: NextRequest, { params }: RouteParams) {
  return withUserIsolation(
    async (req: NextRequest, userId: string, context: any) => {
      const resolvedParams = await params;
      const { id: influencerId } = resolvedParams;

      try {
        // Verificar acesso ao influenciador
        const graphqlUrl = `${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/api/graphql`;

        const response = await fetch(graphqlUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            query: GET_INFLUENCER_BASIC_DATA_QUERY,
            variables: {
              id: influencerId,
              userId: userId
            }
          })
        });

        if (!response.ok) {
          throw new Error(`GraphQL request failed: ${response.status}`);
        }

        const { data, errors } = await response.json();

        if (errors && errors.length > 0) {
          throw new Error(`GraphQL errors: ${errors.map((e: any) => e.message).join(', ')}`);
        }

        if (!data?.influencer) {
          return NextResponse.json(
            { error: 'Influenciador não encontrado' },
            { status: 404 }
          );
        }

        // Desativar todos os tokens do influenciador
        const tokensSnapshot = await db.collection('share_tokens')
          .where('influencerId', '==', influencerId)
          .where('createdBy', '==', userId)
          .where('isActive', '==', true)
          .get();

        const batch = db.batch();
        tokensSnapshot.docs.forEach(doc => {
          batch.update(doc.ref, {
            isActive: false,
            revokedAt: new Date(),
            revokedBy: userId
          });
        });

        await batch.commit();

        return NextResponse.json({
          success: true,
          message: `${tokensSnapshot.docs.length} compartilhamentos revogados`,
          revokedCount: tokensSnapshot.docs.length
        });

      } catch (error: any) {
        console.error('[API_INFLUENCER_SHARE_DELETE] Erro:', {
          influencerId,
          userId,
          error: error.message
        });

        return NextResponse.json(
          { error: 'Erro interno ao revogar compartilhamentos' },
          { status: 500 }
        );
      }
    }
  )(req);
}
