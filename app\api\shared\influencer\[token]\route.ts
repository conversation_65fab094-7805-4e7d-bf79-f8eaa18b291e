import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/firebase-admin';
import { gql } from '@apollo/client';

interface RouteParams {
  params: Promise<{
    token: string;
  }>;
}

// Query GraphQL para buscar dados completos do influenciador para página pública
const GET_INFLUENCER_PUBLIC_DATA_QUERY = `
  query GetInfluencerPublicData($id: ID!, $userId: ID!) {
    influencer(id: $id, userId: $userId) {
      id
      name
      avatar
      backgroundImage
      bio
      age
      gender
      location
      country
      state
      city
      totalFollowers
      totalViews
      engagementRate
      rating
      isVerified
      category
      categories
      mainPlatform
      
      # Redes sociais (campos diretos)
      instagramUsername
      instagramFollowers
      instagramEngagementRate
      instagramAvgViews
      instagramStoriesViews
      instagramReelsViews
      
      tiktokUsername
      tiktokFollowers
      tiktokEngagementRate
      tiktokAvgViews
      tiktokVideoViews
      
      youtubeUsername
      youtubeFollowers
      youtubeSubscribers
      youtubeEngagementRate
      youtubeAvgViews
      youtubeShortsViews
      youtubeLongFormViews
      
      facebookUsername
      facebookFollowers
      facebookEngagementRate
      facebookAvgViews
      facebookViews
      facebookReelsViews
      facebookStoriesViews
      
      twitchUsername
      twitchFollowers
      twitchEngagementRate
      twitchViews
      
      kwaiUsername
      kwaiFollowers
      kwaiEngagementRate
      kwaiViews
      
      # Demographics separado (nova estrutura)
      currentDemographics {
        id
        platform
        audienceGender {
          male
          female
          other
        }
        audienceLocations {
          country
          percentage
        }
        audienceCities {
          city
          percentage
        }
        audienceAgeRange {
          range
          percentage
        }
        captureDate
        isActive
        source
        createdAt
        updatedAt
      }
      
      # Pricing atual (estrutura nova)
      currentPricing {
        id
        services {
          instagram {
            story {
              price
              currency
            }
            reel {
              price
              currency
            }
            post {
              price
              currency
            }
          }
          tiktok {
            video {
              price
              currency
            }
          }
          youtube {
            insertion {
              price
              currency
            }
            dedicated {
              price
              currency
            }
            shorts {
              price
              currency
            }
          }
          facebook {
            post {
              price
              currency
            }
          }
          twitch {
            stream {
              price
              currency
            }
          }
          kwai {
            video {
              price
              currency
            }
          }
        }
        isActive
        validFrom
        validUntil
        notes
        createdAt
        updatedAt
      }
      
      createdAt
      updatedAt
    }
  }
`;

/**
 * GET /api/shared/influencer/[token] - Validar token e retornar dados do influenciador
 */
export async function GET(req: NextRequest, { params }: RouteParams) {
  try {
    const resolvedParams = await params;
    const { token } = resolvedParams;

    console.log('[API_SHARED_INFLUENCER] Validando token:', token);

    // 1. Buscar token no banco
    const tokenDoc = await db.collection('share_tokens').doc(token).get();

    if (!tokenDoc.exists) {
      console.log('[API_SHARED_INFLUENCER] Token não encontrado');
      return NextResponse.json(
        { 
          valid: false, 
          error: 'Token não encontrado',
          code: 'TOKEN_NOT_FOUND'
        },
        { status: 404 }
      );
    }

    const tokenData = tokenDoc.data()!;

    // 2. Verificar se é um token de influenciador
    if (tokenData.type !== 'influencer_profile') {
      console.log('[API_SHARED_INFLUENCER] Token não é de influenciador');
      return NextResponse.json(
        { 
          valid: false, 
          error: 'Token inválido para este tipo de conteúdo',
          code: 'INVALID_TOKEN_TYPE'
        },
        { status: 400 }
      );
    }

    // 3. Verificar se o token está ativo
    if (!tokenData.isActive) {
      console.log('[API_SHARED_INFLUENCER] Token inativo');
      return NextResponse.json(
        { 
          valid: false, 
          error: 'Token foi revogado',
          code: 'TOKEN_REVOKED'
        },
        { status: 403 }
      );
    }

    // 4. Verificar se o token não expirou
    const now = new Date();
    const expiresAt = tokenData.expiresAt.toDate();
    
    if (now > expiresAt) {
      console.log('[API_SHARED_INFLUENCER] Token expirado');
      
      // Marcar token como inativo
      await db.collection('share_tokens').doc(token).update({
        isActive: false,
        expiredAt: now
      });

      return NextResponse.json(
        { 
          valid: false, 
          error: 'Token expirado',
          code: 'TOKEN_EXPIRED',
          expiresAt: expiresAt.toISOString()
        },
        { status: 410 }
      );
    }

    // 5. Buscar dados completos do influenciador via GraphQL
    const influencerId = tokenData.influencerId;
    const createdBy = tokenData.createdBy;

    console.log('[API_SHARED_INFLUENCER] Buscando dados do influenciador:', {
      influencerId,
      createdBy
    });

    const graphqlUrl = `${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/api/graphql`;

    const response = await fetch(graphqlUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: GET_INFLUENCER_PUBLIC_DATA_QUERY,
        variables: {
          id: influencerId,
          userId: createdBy
        }
      })
    });

    if (!response.ok) {
      throw new Error(`GraphQL request failed: ${response.status}`);
    }

    const { data, errors } = await response.json();

    if (errors && errors.length > 0) {
      throw new Error(`GraphQL errors: ${errors.map((e: any) => e.message).join(', ')}`);
    }

    if (!data?.influencer) {
      console.log('[API_SHARED_INFLUENCER] Influenciador não encontrado');
      return NextResponse.json(
        {
          valid: false,
          error: 'Influenciador não encontrado',
          code: 'INFLUENCER_NOT_FOUND'
        },
        { status: 404 }
      );
    }

    // 6. Registrar acesso
    await db.collection('share_tokens').doc(token).update({
      accessCount: (tokenData.accessCount || 0) + 1,
      lastAccessed: now
    });

    // 7. Preparar dados para resposta (remover informações sensíveis)
    const influencer = data.influencer;
    const publicData = {
      id: influencer.id,
      name: influencer.name,
      avatar: influencer.avatar,
      backgroundImage: influencer.backgroundImage,
      bio: influencer.bio,
      age: influencer.age,
      gender: influencer.gender,
      location: influencer.location,
      country: influencer.country,
      state: influencer.state,
      city: influencer.city,
      totalFollowers: influencer.totalFollowers,
      totalViews: influencer.totalViews,
      engagementRate: influencer.engagementRate,
      rating: influencer.rating,
      isVerified: influencer.isVerified,
      category: influencer.category,
      categories: influencer.categories,
      mainPlatform: influencer.mainPlatform,
      
      // Redes sociais (sem informações de contato)
      platforms: {
        instagram: {
          username: influencer.instagramUsername,
          followers: influencer.instagramFollowers,
          engagementRate: influencer.instagramEngagementRate,
          avgViews: influencer.instagramAvgViews,
          storiesViews: influencer.instagramStoriesViews,
          reelsViews: influencer.instagramReelsViews
        },
        tiktok: {
          username: influencer.tiktokUsername,
          followers: influencer.tiktokFollowers,
          engagementRate: influencer.tiktokEngagementRate,
          avgViews: influencer.tiktokAvgViews,
          videoViews: influencer.tiktokVideoViews
        },
        youtube: {
          username: influencer.youtubeUsername,
          followers: influencer.youtubeFollowers,
          subscribers: influencer.youtubeSubscribers,
          engagementRate: influencer.youtubeEngagementRate,
          avgViews: influencer.youtubeAvgViews,
          shortsViews: influencer.youtubeShortsViews,
          longFormViews: influencer.youtubeLongFormViews
        },
        facebook: {
          username: influencer.facebookUsername,
          followers: influencer.facebookFollowers,
          engagementRate: influencer.facebookEngagementRate,
          avgViews: influencer.facebookAvgViews,
          views: influencer.facebookViews,
          reelsViews: influencer.facebookReelsViews,
          storiesViews: influencer.facebookStoriesViews
        },
        twitch: {
          username: influencer.twitchUsername,
          followers: influencer.twitchFollowers,
          engagementRate: influencer.twitchEngagementRate,
          views: influencer.twitchViews
        },
        kwai: {
          username: influencer.kwaiUsername,
          followers: influencer.kwaiFollowers,
          engagementRate: influencer.kwaiEngagementRate,
          views: influencer.kwaiViews
        }
      },
      
      // Demografia
      currentDemographics: influencer.currentDemographics,
      
      // Pricing (apenas se disponível e ativo)
      currentPricing: influencer.currentPricing?.isActive ? influencer.currentPricing : null,
      
      // Metadados do compartilhamento
      shareInfo: {
        sharedBy: tokenData.influencerName,
        sharedAt: tokenData.createdAt.toDate(),
        expiresAt: tokenData.expiresAt.toDate()
      }
    };

    console.log('[API_SHARED_INFLUENCER] Dados retornados com sucesso:', {
      influencerId,
      name: influencer.name,
      platforms: Object.keys(publicData.platforms).filter(p => publicData.platforms[p].username)
    });

    return NextResponse.json({
      valid: true,
      data: publicData
    });

  } catch (error: any) {
    console.error('[API_SHARED_INFLUENCER] Erro:', {
      error: error.message,
      stack: error.stack
    });

    return NextResponse.json(
      { 
        valid: false,
        error: 'Erro interno do servidor',
        code: 'INTERNAL_ERROR'
      },
      { status: 500 }
    );
  }
}
