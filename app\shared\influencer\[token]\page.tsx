'use client'

import React, { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import Head from 'next/head'
import Image from 'next/image'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Skeleton } from '@/components/ui/skeleton'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  Share2, 
  ExternalLink, 
  Clock, 
  Shield, 
  AlertTriangle,
  CheckCircle,
  Users,
  TrendingUp,
  Eye
} from 'lucide-react'
import { toast } from 'sonner'

// Componentes
import { InfluencerProfilePanel } from '@/components/influencer/influencer-profile-panel'
import { ThemeProvider } from '@/components/theme-provider'
import { ThemeToggle } from '@/components/ui/theme-toggle'

interface SharedInfluencerData {
  valid: boolean
  data?: {
    id: string
    name: string
    avatar?: string
    backgroundImage?: string
    bio?: string
    age?: number
    gender?: string
    location?: string
    country?: string
    state?: string
    city?: string
    totalFollowers?: number
    totalViews?: number
    engagementRate?: number
    rating?: number
    isVerified?: boolean
    category?: string
    categories?: string[]
    mainPlatform?: string
    platforms?: any
    currentDemographics?: any[]
    currentPricing?: any
    shareInfo?: {
      sharedBy: string
      sharedAt: string
      expiresAt: string
    }
  }
  error?: string
  code?: string
}

// Componente para meta tags dinâmicas
function DynamicMetaTags({ influencer }: { influencer: any }) {
  const title = `Perfil de ${influencer.name} - Influenciador Digital`
  const description = influencer.bio
    ? `${influencer.bio.substring(0, 150)}...`
    : `Confira o perfil completo de ${influencer.name}, influenciador digital com ${influencer.totalFollowers?.toLocaleString() || '0'} seguidores.`

  const imageUrl = influencer.avatar || '/default-avatar.png'
  const url = typeof window !== 'undefined' ? window.location.href : ''

  return (
    <Head>
      {/* Meta tags básicas */}
      <title>{title}</title>
      <meta name="description" content={description} />
      <meta name="keywords" content={`${influencer.name}, influenciador, ${influencer.categories?.join(', ') || ''}, ${influencer.mainPlatform || ''}`} />
      <meta name="author" content={influencer.name} />
      <meta name="robots" content="index, follow" />
      <meta name="viewport" content="width=device-width, initial-scale=1.0" />

      {/* Open Graph / Facebook */}
      <meta property="og:type" content="profile" />
      <meta property="og:title" content={title} />
      <meta property="og:description" content={description} />
      <meta property="og:image" content={imageUrl} />
      <meta property="og:url" content={url} />
      <meta property="og:site_name" content="Influ DM" />
      <meta property="profile:first_name" content={influencer.name?.split(' ')[0] || ''} />
      <meta property="profile:last_name" content={influencer.name?.split(' ').slice(1).join(' ') || ''} />
      <meta property="profile:username" content={influencer.instagramUsername || influencer.tiktokUsername || ''} />

      {/* Twitter Cards */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={title} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content={imageUrl} />
      <meta name="twitter:creator" content={`@${influencer.instagramUsername || influencer.tiktokUsername || influencer.name}`} />

      {/* Dados estruturados JSON-LD */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "Person",
            "name": influencer.name,
            "description": description,
            "image": imageUrl,
            "url": url,
            "sameAs": [
              influencer.instagramUsername ? `https://instagram.com/${influencer.instagramUsername}` : null,
              influencer.tiktokUsername ? `https://tiktok.com/@${influencer.tiktokUsername}` : null,
              influencer.youtubeUsername ? `https://youtube.com/@${influencer.youtubeUsername}` : null,
              influencer.facebookUsername ? `https://facebook.com/${influencer.facebookUsername}` : null,
              influencer.twitchUsername ? `https://twitch.tv/${influencer.twitchUsername}` : null,
              influencer.kwaiUsername ? `https://kwai.com/@${influencer.kwaiUsername}` : null
            ].filter(Boolean),
            "jobTitle": "Influenciador Digital",
            "worksFor": {
              "@type": "Organization",
              "name": "Influ DM"
            },
            "knowsAbout": influencer.categories || [],
            "audience": {
              "@type": "Audience",
              "audienceType": "Seguidores",
              "geographicArea": influencer.location || influencer.country || "Brasil"
            }
          })
        }}
      />

      {/* Meta tags adicionais para SEO */}
      <meta name="theme-color" content="#ff0074" />
      <meta name="msapplication-TileColor" content="#ff0074" />
      <link rel="canonical" href={url} />

      {/* Preconnect para otimização */}
      <link rel="preconnect" href="https://fonts.googleapis.com" />
      <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
    </Head>
  )
}

export default function SharedInfluencerPage() {
  const params = useParams()
  const token = params?.token as string
  
  const [data, setData] = useState<SharedInfluencerData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Buscar dados do influenciador
  useEffect(() => {
    if (!token) {
      setError('Token não fornecido')
      setLoading(false)
      return
    }

    const fetchInfluencerData = async () => {
      try {
        console.log('[SHARED_INFLUENCER] Buscando dados para token:', token)
        
        const response = await fetch(`/api/shared/influencer/${token}`)
        const result = await response.json()

        console.log('[SHARED_INFLUENCER] Resposta da API:', result)

        if (!response.ok) {
          throw new Error(result.error || 'Erro ao carregar dados')
        }

        if (!result.valid) {
          throw new Error(result.error || 'Token inválido')
        }

        setData(result)
        setError(null)
      } catch (err: any) {
        console.error('[SHARED_INFLUENCER] Erro:', err)
        setError(err.message)
        setData(null)
      } finally {
        setLoading(false)
      }
    }

    fetchInfluencerData()
  }, [token])

  // Função para compartilhar
  const handleShare = async () => {
    if (!data?.data) return

    try {
      if (navigator.share) {
        await navigator.share({
          title: `Perfil de ${data.data.name}`,
          text: `Confira o perfil completo de ${data.data.name}`,
          url: window.location.href
        })
      } else {
        await navigator.clipboard.writeText(window.location.href)
        toast.success('Link copiado para a área de transferência!')
      }
    } catch (err) {
      console.error('Erro ao compartilhar:', err)
      toast.error('Erro ao compartilhar')
    }
  }

  // Loading state
  if (loading) {
    return (
      <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
        <div className="min-h-screen bg-background">
          {/* Header */}
          <header className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
            <div className="container mx-auto px-4 py-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <Image
                    src="/logo.svg"
                    alt="Influ DM"
                    width={120}
                    height={40}
                    className="h-8 w-auto"
                  />
                </div>
                <ThemeToggle />
              </div>
            </div>
          </header>

          {/* Content */}
          <main className="container mx-auto px-4 py-8">
            <div className="max-w-4xl mx-auto">
              <Card>
                <CardHeader>
                  <div className="flex items-center gap-4">
                    <Skeleton className="h-16 w-16 rounded-full" />
                    <div className="space-y-2">
                      <Skeleton className="h-6 w-48" />
                      <Skeleton className="h-4 w-32" />
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <Skeleton className="h-32 w-full" />
                    <Skeleton className="h-24 w-full" />
                    <Skeleton className="h-40 w-full" />
                  </div>
                </CardContent>
              </Card>
            </div>
          </main>
        </div>
      </ThemeProvider>
    )
  }

  // Error state
  if (error || !data?.valid) {
    return (
      <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
        <div className="min-h-screen bg-background">
          {/* Header */}
          <header className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
            <div className="container mx-auto px-4 py-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <Image
                    src="/logo.svg"
                    alt="Influ DM"
                    width={120}
                    height={40}
                    className="h-8 w-auto"
                  />
                </div>
                <ThemeToggle />
              </div>
            </div>
          </header>

          {/* Error Content */}
          <main className="container mx-auto px-4 py-8">
            <div className="max-w-2xl mx-auto">
              <Alert variant="destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  <div className="space-y-2">
                    <p className="font-semibold">Não foi possível carregar o perfil</p>
                    <p>{error || 'Token inválido ou expirado'}</p>
                    {data?.code === 'TOKEN_EXPIRED' && (
                      <p className="text-sm">Este link de compartilhamento expirou. Solicite um novo link ao proprietário do perfil.</p>
                    )}
                    {data?.code === 'TOKEN_NOT_FOUND' && (
                      <p className="text-sm">Este link de compartilhamento não existe ou foi removido.</p>
                    )}
                    {data?.code === 'TOKEN_REVOKED' && (
                      <p className="text-sm">Este link de compartilhamento foi revogado pelo proprietário.</p>
                    )}
                  </div>
                </AlertDescription>
              </Alert>
            </div>
          </main>
        </div>
      </ThemeProvider>
    )
  }

  const influencer = data.data!

  return (
    <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
      {/* Meta tags dinâmicas */}
      <DynamicMetaTags influencer={influencer} />

      <div className="min-h-screen bg-background">
        {/* Header */}
        <header className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 sticky top-0 z-50">
          <div className="container mx-auto px-4 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <Image
                  src="/logo.svg"
                  alt="Influ DM"
                  width={120}
                  height={40}
                  className="h-8 w-auto"
                />
             
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleShare}
                  className="flex items-center gap-2"
                >
                  <Share2 className="h-4 w-4" />
                  Compartilhar
                </Button>
                <ThemeToggle />
              </div>
            </div>
          </div>
        </header>

        {/* Hero Section */}
        <section className="relative">
          {influencer.backgroundImage && (
            <div 
              className="h-48 bg-cover bg-center bg-no-repeat"
              style={{ backgroundImage: `url(${influencer.backgroundImage})` }}
            >
              <div className="absolute inset-0 bg-black/20" />
            </div>
          )}
          
        
        </section>

        {/* Main Content */}
        <main className="container mx-auto px-4 pb-8">
          <div className="max-w-4xl mx-auto">
            <Card>
              <CardContent className="p-0">
                <InfluencerProfilePanel
                  influencer={influencer}
                  isPublic={true}
                  showAdminInfo={false}
                  showShareButton={false}
                  className="border-0"
                />
              </CardContent>
            </Card>

          
          </div>
        </main>
      </div>
    </ThemeProvider>
  )
}
