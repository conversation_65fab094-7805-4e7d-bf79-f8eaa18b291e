'use client';

import { useState, useEffect, useCallback, useMemo } from 'react';
import { useFormContext } from 'react-hook-form';
import { useBrandsGraphQL, useBrandInfluencerAssociations, useBrandInfluencerMutations } from '@/hooks/use-graphql-influencers';
import { useAuth } from '@/hooks/use-auth-v2';
import { useFirebaseAuth } from '@/hooks/use-clerk-auth';
import { useToast } from '@/hooks/use-toast';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';

import { Building2, Search, Check, X } from 'lucide-react';
import { cn } from '@/lib/utils';
import { InfluencerFormData } from '@/types/influencer-form';

interface BrandAssociation {
  id: string;
  brandId: string;
  brandName: string;
  brandLogo?: string;
}

interface Brand {
  id: string;
  name: string;
  logo?: string;
  industry?: string;
}

export function BrandsAssociationSection() {
  const { currentUser } = useAuth();
  const { firebaseUser } = useFirebaseAuth();
  const { toast } = useToast();
  const { brands, loading: brandsLoading } = useBrandsGraphQL(currentUser?.id || '');
  const { watch, setValue, getValues, formState } = useFormContext<InfluencerFormData>();
  
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  // Buscar influencer ID do formulário ou dados iniciais para verificar associações existentes
  // Obtemos o ID através do contexto React Hook Form de forma segura
  const formData = getValues() as any;
  const influencerId = formData.id || formData.influencerId;
  
  // Logs removidos para limpar console
  
  // Buscar associações existentes se estivermos no modo de edição
  const { associations, loading: associationsLoading, refetch: refetchAssociations } = useBrandInfluencerAssociations(
    currentUser?.id || '',
    { influencerId: influencerId || undefined }
  );

  // Hook para mutations de associações
  const { deleteBrandInfluencerAssociation } = useBrandInfluencerMutations();

  // Observar mudanças no formulário - agora é array de strings
  const formBrands = watch('brands') || [];

  // Estado local independente para evitar loops
  const [selectedBrandIds, setSelectedBrandIds] = useState<string[]>([]);

  // Verificar se estamos no modo de edição (se há initialData)
  const isEditMode = formState.isDirty === false && formBrands.length > 0;

  // O hook GraphQL já carrega as associações automaticamente

  // Sincronizar apenas quando os dados do formulário mudarem externamente
  useEffect(() => {
    if (Array.isArray(formBrands) && formBrands.length !== selectedBrandIds.length) {
      setSelectedBrandIds(formBrands);
    }
  }, [formBrands.length]); // Usar apenas o length para evitar comparações profundas

  // Inicializar estado local na primeira renderização
  useEffect(() => {
    const initialBrands = getValues('brands') || [];
    if (initialBrands.length > 0 && selectedBrandIds.length === 0) {
      setSelectedBrandIds(initialBrands);
    }
  }, []); // Executar apenas uma vez

  // Forçar refetch das associações quando o influencerId mudar ou componente montar
  useEffect(() => {
    if (influencerId && currentUser?.id) {
      refetchAssociations();
    }
  }, [influencerId, currentUser?.id, refetchAssociations]);

  // Filtrar marcas com base na busca - memoizado
  const filteredBrands = useMemo(() => {
    // Logs removidos para limpar console
    
    // IDs das marcas já associadas
    const associatedBrandIds = associations.map((assoc: any) => assoc.brandId);
    
    // Se ainda está carregando as associações, não filtrar marcas
    if (associationsLoading) {
      return brands.filter((brand: Brand) => {
        return brand.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
               brand.industry?.toLowerCase().includes(searchTerm.toLowerCase());
      });
    }
    
    const filtered = brands.filter((brand: Brand) => {
      // Se não temos influencerId (modo criação), não excluir nenhuma marca
      if (!influencerId) {
        return brand.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
               brand.industry?.toLowerCase().includes(searchTerm.toLowerCase());
      }
      
      // Excluir marcas já associadas apenas no modo de edição
      if (associatedBrandIds.includes(brand.id)) {
        return false;
      }
      
      // Aplicar filtro de busca
      const matchesSearch = brand.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           brand.industry?.toLowerCase().includes(searchTerm.toLowerCase());
      
      return matchesSearch;
    });
    
    // Logs removidos para limpar console
    
    return filtered;
  }, [brands, searchTerm, associations, associationsLoading, influencerId]);

  // Verificar se uma marca está selecionada - memoizado
  const isBrandSelected = useCallback((brandId: string) => {
    return selectedBrandIds.includes(brandId);
  }, [selectedBrandIds]);

  // Atualizar formulário apenas quando necessário
  const updateForm = useCallback((newBrandIds: string[]) => {
    setValue('brands', newBrandIds, { shouldValidate: false, shouldDirty: true });
  }, [setValue]);

  // Função para remover associação do backend (agora apenas para as marcas já associadas)
  const removeExistingAssociation = useCallback(async (associationId: string) => {
    try {
      // Deletar via GraphQL usando o ID da associação diretamente
      await deleteBrandInfluencerAssociation(associationId);
      
      // Atualizar lista de associações
      await refetchAssociations();
      
      return true;
    } catch (error) {
      console.error('[BRANDS_SECTION] Erro ao remover associação existente:', error);
      return false;
    }
  }, [deleteBrandInfluencerAssociation, refetchAssociations]);

  // Adicionar/remover marca às seleções (APENAS estado local)
  const handleBrandToggle = useCallback((brandId: string, checked: boolean) => {
    let newBrandIds: string[];

    if (checked) {
      // Adicionar marca
      newBrandIds = [...selectedBrandIds, brandId];
    } else {
      // Remover marca
      newBrandIds = selectedBrandIds.filter(id => id !== brandId);
    }

    setSelectedBrandIds(newBrandIds);
    updateForm(newBrandIds);
  }, [selectedBrandIds, updateForm]);

  // Remover marca específica das selecionadas (APENAS estado local)
  const handleRemoveBrand = useCallback((brandId: string) => {
    handleBrandToggle(brandId, false);
  }, [handleBrandToggle]);

  // Obter dados da marca por ID - memoizado
  const getBrandData = useCallback((brandId: string) => {
    return brands.find((brand: Brand) => brand.id === brandId);
  }, [brands]);

  return (
    <div className="space-y-6">
      
      {/* Marcas já associadas ao influenciador (modo de edição) */}
      {influencerId && (
        <Card className="border-green-200 dark:border-green-800 bg-green-50/50 dark:bg-green-900/10">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2">
              <Check className="h-4 w-4 text-green-600" />
              Marcas já Associadas {associationsLoading ? '(carregando...)' : `(${associations.length})`}
            </CardTitle>
            <p className="text-sm text-muted-foreground">
              {associationsLoading 
                ? 'Verificando associações existentes...'
                : associations.length > 0
                ? 'Essas marcas já estão associadas a este influenciador.'
                : 'Nenhuma marca associada ainda.'
              }
            </p>
          </CardHeader>
          <CardContent>
            {associationsLoading ? (
              <div className="text-center py-4">
                <div className="inline-flex items-center gap-2 text-muted-foreground">
                  <div className="w-4 h-4 border-2 border-muted border-t-[#ff0074] rounded-full animate-spin"></div>
                  Carregando associações...
                </div>
              </div>
            ) : associations.length > 0 ? (
              <div className="flex flex-wrap gap-2">
                {associations
                  .sort((a: any, b: any) => (a.brandName || '').localeCompare(b.brandName || ''))
                  .map((association: any) => (
                  <div
                    key={association.id}
                    className="flex items-center gap-2 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-full px-3 py-1 group"
                  >
                    <Avatar className="h-6 w-6">
                      <AvatarImage src={association.brandLogo} />
                      <AvatarFallback className="text-xs">
                        {association.brandName?.substring(0, 2)?.toUpperCase() || 'BR'}
                      </AvatarFallback>
                    </Avatar>
                    <span className="text-sm font-medium">{association.brandName || 'Marca não encontrada'}</span>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={async () => {
                        setIsLoading(true);
                        const success = await removeExistingAssociation(association.id);
                        setIsLoading(false);
                        
                        if (success) {
                          toast({
                            title: "Associação removida",
                            description: `A associação com ${association.brandName} foi removida.`,
                          });
                        } else {
                          toast({
                            title: "Erro ao remover",
                            description: "Não foi possível remover a associação. Tente novamente.",
                            variant: "destructive",
                          });
                        }
                      }}
                      disabled={isLoading}
                      className="h-4 w-4 p-0 hover:bg-red-100 hover:text-red-600 opacity-0 group-hover:opacity-100 transition-opacity"
                    >
                      {isLoading ? (
                        <div className="w-3 h-3 border border-red-500 border-t-transparent rounded-full animate-spin" />
                      ) : (
                        <X className="h-3 w-3" />
                      )}
                    </Button>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-4 text-muted-foreground">
                <Building2 className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p className="text-sm">Nenhuma marca associada ainda</p>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Resumo das marcas selecionadas */}
      {selectedBrandIds.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Check className="h-4 text-xs w-4 text-green-600" />
              Marcas Selecionadas ({selectedBrandIds.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              {selectedBrandIds
                .map((brandId) => ({ brandId, brand: getBrandData(brandId) }))
                .filter(({ brand }) => brand !== undefined)
                .sort((a, b) => (a.brand?.name || '').localeCompare(b.brand?.name || ''))
                .map(({ brandId, brand }) => {
                if (!brand) return null;

                return (
                  <div
                    key={brandId}
                    className="flex items-center gap-2 bg-[#ff0074]/10 border border-[#ff0074]/20 rounded-full px-3 py-1 group"
                  >
                    <Avatar className="h-6 w-6">
                      <AvatarImage src={brand.logo} alt={brand.name} />
                      <AvatarFallback className="bg-[#ff0074] text-white text-xs">
                        {brand.name.substring(0, 2).toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    <span className="text-sm font-medium">{brand.name}</span>
                    <button
                      type="button"
                      onClick={() => handleRemoveBrand(brandId)}
                      className="ml-1 p-1 rounded-full hover:bg-red-100 dark:hover:bg-red-900/20 text-red-500 hover:text-red-700 dark:hover:text-red-400 transition-colors opacity-0 group-hover:opacity-100"
                      title="Remover marca"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Seleção de marcas */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building2 className="h-4 w-4" />
            {influencerId ? 'Adicionar Novas Marcas' : 'Selecionar Marcas'}
          </CardTitle>
          {influencerId && (
            <p className="text-sm text-muted-foreground">
              {associations.length > 0 
                ? 'Selecione marcas adicionais para associar a este influenciador.' 
                : 'Selecione marcas para associar a este influenciador.'}
            </p>
          )}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Buscar marcas..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </CardHeader>
        <CardContent>
          {/* Mostrar loading apenas se as marcas ainda não foram carregadas */}
          {brandsLoading ? (
            <div className="text-center py-8">
              <div className="inline-flex items-center gap-2 text-muted-foreground">
                <div className="w-4 h-4 border-2 border-muted border-t-[#ff0074] rounded-full animate-spin"></div>
                Carregando marcas...
              </div>
            </div>
          ) : filteredBrands.length === 0 ? (
            <div className="text-center py-8">
              <Building2 className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <h4 className="text-lg font-medium mb-2">
                {searchTerm ? 'Nenhuma marca encontrada' : 'Nenhuma marca disponível'}
              </h4>
              <p className="text-muted-foreground mb-4">
                {searchTerm 
                  ? 'Tente ajustar sua busca ou criar uma nova marca.'
                  : 'Crie uma marca primeiro para associá-la aos influenciadores.'
                }
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 gap-3 max-h-96 overflow-y-auto">
              {filteredBrands
                .sort((a: Brand, b: Brand) => a.name.localeCompare(b.name))
                .map((brand: Brand) => {
                const isSelected = isBrandSelected(brand.id);
                
                return (
                  <div
                    key={brand.id}
                    className={cn(
                      "flex items-center justify-between p-3 border rounded-lg transition-colors",
                      isSelected
                        ? "bg-[#ff0074]/5 border-[#ff0074]/20"
                        : "hover:bg-muted/50",
                      "cursor-pointer"
                    )}
                    onClick={() => handleBrandToggle(brand.id, !isSelected)}
                  >
                    <div className="flex items-center gap-3">
                      <Checkbox
                        id={`brand-${brand.id}`}
                        checked={isSelected}
                        onCheckedChange={(checked) => handleBrandToggle(brand.id, checked as boolean)}
                      />
                      <Avatar className="h-10 w-10">
                        <AvatarImage src={brand.logo} alt={brand.name} />
                        <AvatarFallback className="bg-[#ff0074] text-white">
                          {brand.name.substring(0, 2).toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1 min-w-0">
                        <label
                          htmlFor={`brand-${brand.id}`}
                          className="text-sm font-medium cursor-pointer block truncate"
                        >
                          {brand.name}
                        </label>
                        {brand.industry && (
                          <p className="text-xs text-muted-foreground truncate">
                            {brand.industry}
                          </p>
                        )}
                      </div>
                    </div>
                    {isSelected && (
                      <Check className="h-5 w-5 text-[#ff0074]" />
                    )}
                  </div>
                );
              })}
            </div>
          )}
        </CardContent>
      </Card>

    
    </div>
  );
} 


