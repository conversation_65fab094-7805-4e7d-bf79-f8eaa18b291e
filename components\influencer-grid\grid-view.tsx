"use client"

import React, { useState, useEffect, useMemo, useCallback, useRef } from 'react';
import { InfluencerCard } from './influencer-card';
import { Influencer } from './types';
import { useProposalBudgets } from '@/hooks/use-budgets-graphql';
import { gql, useQuery } from '@apollo/client';

// 🆕 TIPOS PARA O GRID VIEW
type InfluencerStatus = 'pendente' | 'aceito' | 'rejeitado' | 'descartado';

interface GridViewProps {
  influencers: Influencer[];
  onInfluencerClick: (influencer: Influencer) => void;
  selectedInfluencers: string[];
  selectedInfluencer?: Influencer | null;
  selectionMode: boolean;
  onToggleSelection: (id: string) => void;
  onDuplicate?: (influencer: Influencer) => void;
  onEdit?: (influencer: Influencer) => void;
  onDelete?: (influencer: Influencer) => void;
  selectedInfluencerId?: string | null;
  selectedProposal?: any;
  userId?: string; // 🆕 Necessário para hooks GraphQL
  // 🔗 NOVO: Prop para lista compartilhada
  isSharedList?: boolean;
}

// 🆕 QUERY PARA BUSCAR STATUS DOS INFLUENCERS NA PROPOSTA
const GET_PROPOSAL_INFLUENCERS_STATUS = gql`
  query GetProposalInfluencersStatus($proposalId: ID!, $userId: ID!) {
    proposalInfluencersStatus(proposalId: $proposalId, userId: $userId) {
      influencerId
      status
      addedAt
      updatedAt
      addedBy
      updatedBy
    }
  }
`;

const GridViewComponent = ({
  influencers,
  onInfluencerClick,
  selectedInfluencers,
  selectedInfluencer,
  selectionMode,
  onToggleSelection,
  onDuplicate,
  onEdit,
  onDelete,
  selectedInfluencerId,
  selectedProposal,
  userId,
  // 🔗 NOVO: Prop para lista compartilhada
  isSharedList = false
}: GridViewProps) => {
  // 🚀 Memoizar IDs dos influencers para detectar mudanças reais na lista
  const influencerIds = useMemo(() =>
    influencers.map(inf => String(inf.id)).sort().join(','),
    [influencers]
  );

  // 🚀 Usar ref para tracking de proposta anterior
  const previousProposalId = useRef<string | null>(null);
  const proposalId = selectedProposal?.id || null;

  // 🚀 BUSCAR STATUS DOS INFLUENCERS NA PROPOSTA via GraphQL (OTIMIZADO)
  const { data: proposalStatusData, loading: statusLoading, error: statusError, refetch } = useQuery(
    GET_PROPOSAL_INFLUENCERS_STATUS,
    {
      variables: {
        proposalId: proposalId || '',
        userId: userId || ''
      },
      skip: !proposalId || !userId,
      fetchPolicy: 'cache-first', // 🚀 Priorizar cache para reduzir requests
      errorPolicy: 'all',
      // 🚀 Remover pollInterval para evitar atualizações desnecessárias
      notifyOnNetworkStatusChange: false // 🚀 Reduzir notificações desnecessárias
    }
  );

  // 🚀 MEMOIZAR o cálculo do statusMap para evitar re-execuções desnecessárias
  const influencersStatus = useMemo(() => {
    // 🚀 Verificações otimizadas
    if (!proposalId || !proposalStatusData?.proposalInfluencersStatus || influencers.length === 0) {
      return {};
    }

    // 🚀 Criar mapa de status baseado nos dados da subcoleção
    const statusMap: Record<string, InfluencerStatus> = {};

    influencers.forEach((influencer: Influencer) => {
      const influencerId = String(influencer.id);

      // Encontrar o status deste influencer na proposta
      const influencerStatusData = proposalStatusData.proposalInfluencersStatus.find(
        (item: any) => item.influencerId === influencerId
      );

      if (influencerStatusData) {
        // Mapear o status do Firebase para o tipo correto
        const status = influencerStatusData.status || 'pendente';
        statusMap[influencerId] = status as InfluencerStatus;
      } else {
        // Se não encontrou o influencer na proposta, provavelmente não foi adicionado ainda
        statusMap[influencerId] = 'pendente';
      }
    });



    return statusMap;
  }, [proposalId, proposalStatusData, influencerIds]); // 🚀 Dependências otimizadas

  // 🚀 REMOVER useEffect redundante - o refetch já acontece automaticamente quando as variáveis da query mudam

  // 🚀 Memoizar função para forçar atualização do status
  const refreshStatus = useCallback(() => {
    // 🚀 Forçar atualização do status dos influencers  
    refetch();
  }, [refetch]);

  // 🚀 Memoizar callbacks para evitar re-renders dos filhos
  const handleInfluencerClick = useCallback((influencer: Influencer) => {
    onInfluencerClick(influencer);
  }, [onInfluencerClick]);

  const handleToggleSelection = useCallback((id: string) => {
    onToggleSelection(id);
  }, [onToggleSelection]);

  return (
    <div className="grid grid-cols-[repeat(auto-fit,minmax(17rem,1fr))] gap-3">
      {influencers.map((influencer: Influencer) => (
        <InfluencerCard
          key={influencer.id}
          influencer={influencer}
          onClick={handleInfluencerClick} // 🚀 Usar callback memoizado
          isSelected={selectionMode ?
            selectedInfluencers.includes(String(influencer.id)) :
            (selectedInfluencer?.id === influencer.id || selectedInfluencerId === String(influencer.id))
          }
          selectionMode={selectionMode}
          onToggleSelection={handleToggleSelection} // 🚀 Usar callback memoizado
          onDuplicate={onDuplicate}
          onEdit={onEdit}
          onDelete={onDelete}
          isSelectedFromUrl={selectedInfluencerId === String(influencer.id)}
          isInProposal={!!proposalId} // 🚀 Usar proposalId memoizado
          proposalId={proposalId} // 🚀 Usar proposalId memoizado
          influencerStatus={influencersStatus[String(influencer.id)] || null}
          // 🔗 NOVO: Passar prop para lista compartilhada
          isSharedList={isSharedList}
        />
      ))}
    </div>
  );
};

// 🚀 Memoizar o componente para evitar re-renders desnecessários
export const GridView = React.memo(GridViewComponent);


