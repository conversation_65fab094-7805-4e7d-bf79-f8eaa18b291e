'use client'

import React, { useState, Suspense } from 'react'
import { useTranslations } from '@/hooks/use-translations'
import { cn } from '@/lib/utils'
import { 
  Calendar, 
  Building, 
  Users, 
  TrendingUp, 
  Info, 
  Eye,
  BarChart3,
  Separator
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { ChartContainer, ChartTooltip, ChartTooltipContent } from '@/components/ui/chart'
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  PieChart, 
  Pie, 
  Cell, 
  LabelList 
} from 'recharts'

// Componentes específicos
import { ZeroLCPInfluencerAvatar } from '@/components/ui/zero-lcp-avatar'
import { LocationBarChart } from '@/components/ui/location-bar-chart'
import { AudienceInterests } from '@/components/ui/audience-interests'
import { SocialScreenshotsSection } from '@/components/ui/social-screenshots-section'

interface InfluencerProfilePanelProps {
  influencer: any
  isPublic?: boolean
  showAdminInfo?: boolean
  showShareButton?: boolean
  onShare?: () => void
  className?: string
}

export function InfluencerProfilePanel({
  influencer,
  isPublic = false,
  showAdminInfo = false,
  showShareButton = false,
  onShare,
  className = ""
}: InfluencerProfilePanelProps) {
  const { t } = useTranslations()
  const [selectedPlatform, setSelectedPlatform] = useState('instagram')

  // Função para formatar localização
  const formatLocationDisplay = (influencer: any) => {
    if (!influencer) return ''
    
    const parts = []
    if (influencer.city) parts.push(influencer.city)
    if (influencer.state) parts.push(influencer.state)
    if (influencer.country && influencer.country !== 'Brasil') parts.push(influencer.country)
    
    return parts.join(', ')
  }

  // Cores das plataformas
  const platformColors = {
    youtube: "#ef4444",
    instagram: "#ec4899", 
    tiktok: "#374151",
    facebook: "#3b82f6",
    twitch: "#8b5cf6",
    kwai: "#f59e0b"
  }

  const platformNames = {
    youtube: "YouTube",
    instagram: "Instagram",
    tiktok: "TikTok", 
    facebook: "Facebook",
    twitch: "Twitch",
    kwai: "Kwai"
  }

  if (!influencer) {
    return (
      <div className={cn("p-6 text-center", className)}>
        <p className="text-muted-foreground">
          {isPublic ? 'Carregando perfil...' : t('panels.contact.select_influencer')}
        </p>
      </div>
    )
  }

  return (
    <div className={cn("p-3 space-y-6", className)}>
      {/* Header do perfil */}
      <div className="flex items-start gap-2 md:gap-4">
        <div className="relative">
          <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full border border-background"></div>
          <ZeroLCPInfluencerAvatar
            influencerName={influencer.name}
            avatarUrl={influencer.avatar}
            size="lg"
            className="w-12 h-12 sm:w-16 sm:h-16"
          />
        </div>
        <div className="flex flex-col flex-1">
          <div className="flex items-center justify-between">
            <h2 className="text-sm sm:text-lg font-bold mb-2">{influencer.name}</h2>
            {showShareButton && onShare && (
              <Button
                variant="outline"
                size="sm"
                onClick={onShare}
                className="ml-2"
              >
                Compartilhar
              </Button>
            )}
          </div>
          
          <p className="text-xs sm:text-sm text-muted-foreground mb-1">
            {/* Idade e Gênero */}
            {influencer.age && (
              <span>{t('panels.contact.years_old', { age: influencer.age })}</span>
            )}
            {influencer.gender && (
              <span>
                {influencer.age ? ', ' : ''}
                {influencer.gender === 'female' ? t('panels.contact.female') : 
                 influencer.gender === 'male' ? t('panels.contact.male') : influencer.gender}
              </span>
            )}
            {/* Localização */}
            {(influencer.age || influencer.gender) && formatLocationDisplay(influencer) && ', '}
            {formatLocationDisplay(influencer)}
          </p>
          
          {/* Email apenas para admins e não público */}
          {showAdminInfo && influencer.email && !isPublic && (
            <p className="text-xs sm:text-sm text-muted-foreground mb-2">{influencer.email}</p>
          )}
          
          <p className="text-xs sm:text-sm text-muted-foreground">
            {influencer.bio || influencer.expertise }
          </p>
        </div>
      </div>

      {/* Informações administrativas */}
      {showAdminInfo && !isPublic && (
        <div className="space-y-4">
          {(influencer.responsibleName || influencer.agencyName || influencer.responsibleCapturer) && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-xs sm:text-sm">
              {influencer.responsibleName && (
                <div className="flex items-center gap-2">
                  <Building className="h-4 w-4 text-muted-foreground" />
                  <span><span className="font-semibold">{t('common.responsible', 'Responsável')}:</span> {influencer.responsibleName}</span>
                </div>
              )}
              {influencer.agencyName && (
                <div className="flex items-center gap-2">
                  <Users className="h-4 w-4 text-muted-foreground" />
                  <span><span className="font-semibold">{t('common.agency', 'Agência')}:</span> {influencer.agencyName}</span>
                </div>
              )}
              {influencer.responsibleCapturer && (
                <div className="flex items-center gap-2">
                  <Users className="h-4 w-4 text-muted-foreground" />
                  <span><span className="font-semibold">{t('common.capturer', 'Captador')}:</span> {influencer.responsibleCapturer}</span>
                </div>
              )}
            </div>
          )}
        </div>
      )}

      {/* Demografia da Audiência */}
      {influencer.currentDemographics && Array.isArray(influencer.currentDemographics) && influencer.currentDemographics.length > 0 && (
        <div className="space-y-6">
          <div className="mb-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="font-semibold text-xs">{t('panels.contact.audience_demographics', 'Demografia da Audiência')}</h3>
            </div>
            
            {/* Abas das Plataformas */}
            <div className="flex flex-wrap gap-1 p-1 bg-muted/50 rounded-full">
              {(() => {
                const platformOrder = ['instagram', 'tiktok', 'youtube', 'facebook', 'twitch', 'kwai']
                
                const availablePlatforms = influencer.currentDemographics
                  .map((d: any) => d.platform)
                  .filter((p: string) => platformOrder.includes(p))
                  .sort((a: string, b: string) => {
                    const indexA = platformOrder.indexOf(a)
                    const indexB = platformOrder.indexOf(b)
                    return indexA - indexB
                  })
                
                // Se a plataforma selecionada não está disponível, selecionar a primeira disponível
                if (!availablePlatforms.includes(selectedPlatform) && availablePlatforms.length > 0) {
                  setSelectedPlatform(availablePlatforms[0])
                }
                
                return availablePlatforms.map((platform: string) => (
                  <button
                    key={platform}
                    onClick={() => setSelectedPlatform(platform)}
                    className={`px-3 py-2 text-xs font-medium rounded-full transition-all duration-200 ${
                      selectedPlatform === platform
                        ? 'text-white shadow-sm transform scale-105'
                        : 'text-muted-foreground hover:text-foreground hover:bg-white/50'
                    }`}
                    style={{
                      backgroundColor: selectedPlatform === platform ? platformColors[platform as keyof typeof platformColors] : 'transparent'
                    }}
                  >
                    {platformNames[platform as keyof typeof platformNames] || platform}
                  </button>
                ))
              })()}
            </div>
          </div>

          {/* Gráfico de Gênero */}
          <div data-tour="gender-chart">
            <div className="flex items-center justify-between mb-3">
              <h4 className="font-semibold text-xs">{t('panels.contact.gender_distribution')}</h4>
            </div>
            <div className="space-y-3">
              {(() => {
                const selectedDemographic = influencer.currentDemographics.find((d: any) => d.platform === selectedPlatform)
                
                if (!selectedDemographic || !selectedDemographic.audienceGender) {
                  return (
                    <div className="text-xs text-muted-foreground p-4 bg-muted/20 rounded-lg text-center">
                      Dados de gênero não disponíveis para {selectedPlatform}
                    </div>
                  )
                }

                const gender = selectedDemographic.audienceGender
                
                const chartData = [
                  {
                    name: "Feminino",
                    value: gender.female || 0,
                    fill: "#ff0074"
                  },
                  {
                    name: "Masculino", 
                    value: gender.male || 0,
                    fill: "#5600ce"
                  }
                ]
                
                if (gender.other && gender.other > 0) {
                  chartData.push({
                    name: "Outros",
                    value: gender.other,
                    fill: "#6b7280"
                  })
                }
                
                return (
                  <div className="flex items-center">
                    <ChartContainer
                      config={{
                        feminino: {
                          label: "Feminino",
                          color: "#ec4899",
                        },
                        masculino: {
                          label: "Masculino",
                          color: "#8b5cf6",
                        },
                        outros: {
                          label: "Outros",
                          color: "#6b7280",
                        },
                      }}
                      className="h-[150px] w-[250px]"
                    >
                      <PieChart>
                        <Pie
                          data={chartData}
                          cx="50%"
                          cy="50%"
                          innerRadius={45}
                          outerRadius={60}
                          paddingAngle={2}
                          dataKey="value"
                        >
                          {chartData.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={entry.fill} />
                          ))}
                        </Pie>
                        <ChartTooltip
                          content={<ChartTooltipContent />}
                          formatter={(value, name) => [`${value}%`, name]}
                        />
                      </PieChart>
                    </ChartContainer>
                    
                    {/* Legenda à direita */}
                    <div className="flex flex-col ml-4 space-y-2">
                      <div className="flex items-center space-x-2">
                        <div className="w-3 h-3 rounded-full" style={{backgroundColor: '#ec4899'}}></div>
                        <span className="text-xs font-medium">Feminino</span>
                        <span className="text-sm font-bold" style={{color: '#ec4899'}}>{gender.female || 0}%</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <div className="w-3 h-3 rounded-full" style={{backgroundColor: '#8b5cf6'}}></div>
                        <span className="text-xs font-medium">Masculino</span>
                        <span className="text-sm font-bold" style={{color: '#8b5cf6'}}>{gender.male || 0}%</span>
                      </div>
                      {gender.other && gender.other > 0 && (
                        <div className="flex items-center space-x-2">
                          <div className="w-3 h-3 rounded-full" style={{backgroundColor: '#6b7280'}}></div>
                          <span className="text-xs font-medium">Outros</span>
                          <span className="text-sm font-bold" style={{color: '#6b7280'}}>{gender.other}%</span>
                        </div>
                      )}
                    </div>
                  </div>
                )
              })()}
            </div>
          </div>
        </div>
      )}

      {/* Faixas Etárias */}
      {influencer.currentDemographics && Array.isArray(influencer.currentDemographics) && influencer.currentDemographics.length > 0 && (
        <div data-tour="age-range-chart">
          <div className="flex items-center justify-between mb-3">
            <h4 className="font-semibold text-xs">{t('audience.age_ranges')}</h4>
          </div>
          <div className="space-y-3">
            {(() => {
              const selectedDemographic = influencer.currentDemographics.find((d: any) => d.platform === selectedPlatform)

              if (!selectedDemographic || !selectedDemographic.audienceAgeRange || selectedDemographic.audienceAgeRange.length === 0) {
                return (
                  <div className="text-xs text-muted-foreground p-4 bg-muted/20 rounded-lg text-center">
                    Dados de faixa etária não disponíveis para {selectedPlatform}
                  </div>
                )
              }

              const ageRangeData = selectedDemographic.audienceAgeRange
                .map((item: any) => ({
                  ageRange: item.range,
                  percentage: item.percentage
                }))
                .sort((a, b) => {
                  const getMinAge = (range: string): number => {
                    const match = range.match(/(\d+)/)
                    return match ? parseInt(match[1]) : 0
                  }
                  return getMinAge(a.ageRange) - getMinAge(b.ageRange)
                })

              const platformColor = platformColors[selectedPlatform as keyof typeof platformColors] || "#ff0074"
              const platformDisplayName = platformNames[selectedPlatform as keyof typeof platformNames] || selectedPlatform

              return (
                <ChartContainer
                  config={{
                    percentage: {
                      label: "Porcentagem",
                      color: platformColor,
                    },
                  }}
                  className="h-[350px] w-full"
                >
                  <BarChart
                    data={ageRangeData}
                    margin={{
                      top: 20,
                      right: 30,
                      left: 20,
                      bottom: 5,
                    }}
                  >
                    <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
                    <XAxis
                      dataKey="ageRange"
                      tick={{ fontSize: 12, fontWeight: 500 }}
                      axisLine={false}
                      tickLine={false}
                    />
                    <YAxis
                      tick={{ fontSize: 11 }}
                      axisLine={false}
                      tickLine={false}
                      tickFormatter={(value) => `${value}%`}
                    />
                    <ChartTooltip
                      content={({ active, payload, label }) => {
                        if (active && payload && payload.length) {
                          return (
                            <div className="bg-background border rounded-lg shadow-lg p-3">
                              <p className="font-semibold text-xs">{label} anos • {platformDisplayName}</p>
                              <p className="text-sm">
                                <span className="font-medium">Audiência:</span> {payload[0]?.value}%
                              </p>
                            </div>
                          )
                        }
                        return null
                      }}
                    />
                    <Bar
                      dataKey="percentage"
                      fill={platformColor}
                      radius={[4, 4, 0, 0]}
                      className="drop-shadow-sm"
                    >
                      <LabelList
                        dataKey="percentage"
                        position="top"
                        style={{
                          fontSize: '12px',
                          fontWeight: '600',
                          fill: 'text-muted-foreground'
                        }}
                        formatter={(value: number) => `${value}%`}
                      />
                    </Bar>
                  </BarChart>
                </ChartContainer>
              )
            })()}
          </div>
        </div>
      )}

      {/* Localização da Audiência - Países */}
      {influencer.currentDemographics && Array.isArray(influencer.currentDemographics) && influencer.currentDemographics.length > 0 && (
        <div data-tour="location-countries-chart">
          <div className="flex items-center justify-between mb-3">
            <h4 className="font-semibold text-xs">{t('audience.countries')}</h4>
          </div>
          <div className="space-y-3">
            {(() => {
              const selectedDemographic = influencer.currentDemographics.find((d: any) => d.platform === selectedPlatform)

              if (!selectedDemographic) {
                return (
                  <div className="text-xs text-muted-foreground p-4 bg-muted/20 rounded-lg text-center">
                    Dados de países não disponíveis para {selectedPlatform}
                  </div>
                )
              }

              const locationData = (() => {
                if (!selectedDemographic.audienceLocations || !Array.isArray(selectedDemographic.audienceLocations)) {
                  return [
                    { location: 'Brazil', percentage: 97.44 },
                    { location: 'United States', percentage: 0.71 },
                    { location: 'Portugal', percentage: 0.44 },
                    { location: 'Spain', percentage: 0.15 }
                  ]
                }

                return selectedDemographic.audienceLocations
                  .filter((locationData: any) => locationData.country && locationData.percentage > 0)
                  .map((locationData: any) => ({
                    location: locationData.country,
                    percentage: locationData.percentage
                  }))
              })()

              return (
                <LocationBarChart
                  data={locationData}
                  maxItems={6}
                />
              )
            })()}
          </div>
        </div>
      )}

      {/* Localização da Audiência - Cidades */}
      {influencer.currentDemographics && Array.isArray(influencer.currentDemographics) && influencer.currentDemographics.length > 0 && (
        <div data-tour="location-cities-chart">
          <div className="flex items-center justify-between mb-3">
            <h4 className="font-semibold text-xs">{t('audience.cities')}</h4>
          </div>
          <div className="space-y-3">
            {(() => {
              const selectedDemographic = influencer.currentDemographics.find((d: any) => d.platform === selectedPlatform)

              if (!selectedDemographic) {
                return (
                  <div className="text-xs text-muted-foreground p-4 bg-muted/20 rounded-lg text-center">
                    Dados de cidades não disponíveis para {selectedPlatform}
                  </div>
                )
              }

              const cityData = (() => {
                if (!selectedDemographic.audienceCities || !Array.isArray(selectedDemographic.audienceCities)) {
                  return [
                    { location: 'Rio de Janeiro', percentage: 50.91 },
                    { location: 'Brasília', percentage: 1.58 },
                    { location: 'São Paulo', percentage: 1.55 },
                    { location: 'Juiz de Fora', percentage: 1.40 },
                    { location: 'Manaus', percentage: 1.16 }
                  ]
                }

                return selectedDemographic.audienceCities
                  .filter((cityData: any) => cityData.city && cityData.percentage > 0)
                  .map((cityData: any) => ({
                    location: cityData.city,
                    percentage: cityData.percentage
                  }))
              })()

              return (
                <LocationBarChart
                  data={cityData}
                  maxItems={6}
                />
              )
            })()}
          </div>
        </div>
      )}

      {/* Mensagem quando não há dados demográficos */}
      {(!influencer.currentDemographics || !Array.isArray(influencer.currentDemographics) || influencer.currentDemographics.length === 0) && !isPublic && (
        <div className="p-4 text-center bg-muted/20 rounded-lg">
          <p className="text-sm text-muted-foreground">
            Dados demográficos não disponíveis para este influenciador
          </p>
        </div>
      )}

      {/* Screenshots das Redes Sociais - Apenas para versão privada */}
      {!isPublic && (
        <Accordion type="single" collapsible className="w-full" data-tour="social-screenshots">
          <AccordionItem value="social-screenshots">
            <AccordionTrigger className="py-2 text-xs font-medium">
              <div className="flex items-center gap-2">
                <BarChart3 className="h-4 w-4" />
                <span className="text-xs sm:text-sm">{t('influencers.social_screenshots')}</span>
              </div>
            </AccordionTrigger>
            <AccordionContent>
              <div className="space-y-2 pt-2">
                <Suspense
                  fallback={
                    <div className="p-4 text-center">
                      <div className="flex items-center justify-center gap-2">
                        <div className="w-4 h-4 border-2 border-primary border-t-transparent rounded-full animate-spin"></div>
                        <span className="text-sm text-muted-foreground">Carregando screenshots...</span>
                      </div>
                    </div>
                  }
                >
                  <SocialScreenshotsSection
                    influencer={influencer}
                    loadOnExpand={true}
                  />
                </Suspense>
              </div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      )}
    </div>
  )
}
