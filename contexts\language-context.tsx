'use client';

import React, { createContext, useContext, useEffect, useState, Suspense } from 'react';
import { useRouter, usePathname, useSearchParams } from 'next/navigation';

// Tipos de idiomas suportados
export type Locale = 'pt' | 'en' | 'es';

export const locales: Locale[] = ['pt', 'en', 'es'];

interface LanguageContextType {
  locale: Locale;
  setLocale: (locale: Locale) => void;
  availableLocales: Locale[];
}

interface LanguageProviderProps {
  children: React.ReactNode;
  initialLocale?: Locale;
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

export function useLanguage() {
  const context = useContext(LanguageContext);
  if (!context) {
    throw new Error('useLanguage deve ser usado dentro de um LanguageProvider');
  }
  return context;
}

// Função para extrair idioma da URL
function getLocaleFromPathname(pathname: string): Locale {
  const segments = pathname.split('/');
  const firstSegment = segments[1] as Locale;
  
  if (locales.includes(firstSegment)) {
    return firstSegment;
  }
  
  return 'pt'; // fallback para português
}

// Função para detectar idioma do navegador
function getLocaleFromBrowser(): Locale {
  if (typeof window === 'undefined') return 'pt';
  
  const browserLang = navigator.language.toLowerCase();
  if (browserLang.startsWith('en')) return 'en';
  if (browserLang.startsWith('es')) return 'es';
  return 'pt';
}

export function LanguageProvider({ children, initialLocale }: LanguageProviderProps) {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  // Detectar idioma da URL, localStorage ou navegador
  const [locale, setLocaleState] = useState<Locale>(() => {
    if (typeof window !== 'undefined') {
      // 1. Prioridade: URL
      if (pathname) {
        const urlLocale = getLocaleFromPathname(pathname);
        if (urlLocale !== 'pt') { // Só usar se não for o padrão
          
          return urlLocale;
        }
      }
      
      // 2. Prioridade: localStorage
      const storedLocale = localStorage.getItem('preferred-locale') as Locale;
      if (storedLocale && locales.includes(storedLocale)) {
        
        return storedLocale;
      }
      
      // 3. Prioridade: navegador
      const browserLocale = getLocaleFromBrowser();
      
      return browserLocale;
    }
    
    return initialLocale || 'pt';
  });

  // Atualizar idioma quando a URL mudar
  useEffect(() => {
    if (!pathname) return;
    
    const urlLocale = getLocaleFromPathname(pathname);
    if (urlLocale !== locale) {
        
      setLocaleState(urlLocale);
      document.documentElement.lang = urlLocale;
      localStorage.setItem('preferred-locale', urlLocale);
    }
  }, [pathname, locale]);

  // Definir idioma inicial no HTML
  useEffect(() => {
    document.documentElement.lang = locale;
    
  }, [locale]);

  const setLocale = (newLocale: Locale) => {
    if (newLocale === locale || !pathname) return;
    
    
    
    // Salvar preferência
    localStorage.setItem('preferred-locale', newLocale);
    
    // Construir nova URL com o novo idioma
    const currentLocale = getLocaleFromPathname(pathname);
    let newPathname = pathname;
    
    if (currentLocale && locales.includes(currentLocale)) {
      // Substituir idioma existente
      newPathname = pathname.replace(`/${currentLocale}`, `/${newLocale}`);
    } else {
      // Adicionar idioma no início
      newPathname = `/${newLocale}${pathname}`;
    }
    
    // Preservar query parameters se existirem
    const queryString = searchParams?.toString() || '';
    const fullUrl = queryString ? `${newPathname}?${queryString}` : newPathname;
    

    
    // Navegar para a nova URL com query parameters preservados
    router.push(fullUrl);
  };

  const value: LanguageContextType = {
    locale,
    setLocale,
    availableLocales: locales,
  };

  return (
    <LanguageContext.Provider value={value}>
      {children}
    </LanguageContext.Provider>
  );
}

// 🔒 CORREÇÃO: Wrapper com Suspense para resolver erro do useSearchParams
function LanguageProviderFallback({ children }: { children: React.ReactNode }) {
  // Estado padrão enquanto carrega
  const value: LanguageContextType = {
    locale: 'pt',
    setLocale: () => {},
    availableLocales: locales,
  };

  return (
    <LanguageContext.Provider value={value}>
      {children}
    </LanguageContext.Provider>
  );
}

// 🔒 CORREÇÃO: Provider principal com Suspense
export function LanguageProviderWrapper({ children, initialLocale }: LanguageProviderProps) {
  return (
    <Suspense fallback={<LanguageProviderFallback>{children}</LanguageProviderFallback>}>
      <LanguageProvider initialLocale={initialLocale}>
        {children}
      </LanguageProvider>
    </Suspense>
  );
}
