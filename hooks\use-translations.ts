'use client';

import { useLanguage } from '@/contexts/language-context';
import { useState, useEffect, useCallback, useMemo } from 'react';

type Messages = Record<string, any>;

export function useTranslations() {
  const { locale } = useLanguage();
  const [messages, setMessages] = useState<Messages>({});
  const [isLoading, setIsLoading] = useState(true);

  // Memoizar a função de carregamento para evitar recriações desnecessárias
  const loadMessages = useCallback(async () => {
    setIsLoading(true);
    
    
    try {
      const messagesModule = await import(`@/messages/${locale}.json`);
      setMessages(messagesModule.default);
    
    } catch (error) {
      
      // Fallback para português se houver erro
      try {
        const fallbackModule = await import('@/messages/pt.json');
        setMessages(fallbackModule.default);
        
      } catch (fallbackError) {
        
        setMessages({});
      }
    } finally {
      setIsLoading(false);
    }
  }, [locale]);

  useEffect(() => {
    loadMessages();
  }, [loadMessages]);

  // Memoizar a função de tradução para otimizar performance
  const t = useCallback((key: string, params?: Record<string, string | number>): string | any[] => {
    // Se ainda está carregando e não temos mensagens, aguardar um pouco mais
    if (isLoading && Object.keys(messages).length === 0) {
      // Retornar indicador único baseado na chave para evitar duplicações
      const keyParts = key.split('.');
      const lastPart = keyParts[keyParts.length - 1];
      return `${lastPart}...`; // Usa a última parte da chave + '...'
    }

    const keys = key.split('.');
    let value: any = messages;

    for (const k of keys) {
      if (value && typeof value === 'object' && k in value) {
        value = value[k];
      } else {
        
        
        // Retornar uma tradução padrão baseada na chave ao invés da chave completa
        const lastPart = keys[keys.length - 1];
        return lastPart.charAt(0).toUpperCase() + lastPart.slice(1).replace(/_/g, ' ');
      }
    }

    // Se o valor é um array, retorná-lo diretamente (para casos como popular_locations)
    if (Array.isArray(value)) {
      return value;
    }

    if (typeof value !== 'string') {
        
      const lastPart = keys[keys.length - 1];
      return lastPart.charAt(0).toUpperCase() + lastPart.slice(1).replace(/_/g, ' ');
    }

    // Substituir parâmetros na string
    if (params) {
      return value.replace(/\{(\w+)\}/g, (match, paramKey) => {
        return params[paramKey]?.toString() || match;
      });
    }

    return value;
  }, [messages, isLoading, locale]);

  // Memoizar o retorno para evitar recriações desnecessárias
  return useMemo(() => ({
    t,
    isLoading,
    locale,
    messages
  }), [t, isLoading, locale, messages]);
}
