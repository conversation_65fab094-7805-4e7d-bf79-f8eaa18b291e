// APOLLO CLIENT CONFIGURATION
// Configuração do cliente GraphQL para o frontend

import { ApolloClient, InMemoryCache, createHttpLink, from } from '@apollo/client';
import { setContext } from '@apollo/client/link/context';
import { onError } from '@apollo/client/link/error';
import { RetryLink } from '@apollo/client/link/retry';

// Declaração de tipos para Clerk
declare global {
  interface Window {
    Clerk?: {
      session?: {
        getToken: () => Promise<string>;
      };
    };
  }
}

// HTTP Link para o endpoint GraphQL
const httpLink = createHttpLink({
  uri: '/api/graphql',
  credentials: 'same-origin',
});

// Auth Link - adiciona token de autenticação Clerk
const authLink = setContext(async (_, { headers }) => {
  let token = null;
  
  if (typeof window !== 'undefined') {
    try {
      // 🔐 MELHORADO: Múltiplas formas de obter token do Clerk
      
      // Método 1: window.Clerk.session
      if (window.Clerk?.session) {
        try {
          token = await window.Clerk.session.getToken();
          
        } catch (error) {
            
        }
      }
      
      // Método 2: Tentar obter do cookie '__session' do Clerk (fallback)
      if (!token) {
        const sessionCookie = document.cookie
          .split('; ')
          .find(row => row.startsWith('__session='));
        
        if (sessionCookie) {
          token = sessionCookie.split('=')[1];
          
        }
      }
      
      // Método 3: Verificar se há token no localStorage (último recurso)
      if (!token) {
        const storedToken = localStorage.getItem('__clerk_session_token');
        if (storedToken) {
          token = storedToken;
          
        }
      }
      
    } catch (error) {
      
    }
  }
  

  
  return {
    headers: {
      ...headers,
      authorization: token ? `Bearer ${token}` : '',
    }
  };
});

// Error Link - tratamento de erros
const errorLink = onError(({ graphQLErrors, networkError, operation, forward }) => {
  if (networkError) {
    // Se erro 401, redirecionar para login
    if ('statusCode' in networkError && networkError.statusCode === 401) {
      if (typeof window !== 'undefined') {
        localStorage.removeItem('authToken');
        window.location.href = '/login';
      }
    }
  }
});

// Retry Link - repetir requests em caso de falha
const retryLink = new RetryLink({
  delay: {
    initial: 300,
    max: Infinity,
    jitter: true
  },
  attempts: {
    max: 3,
    retryIf: (error, _operation) => !!error
  }
});

// Configuração do Cache com type policies
const cache = new InMemoryCache({
  typePolicies: {
    // Configuração para Influencer
    Influencer: {
      keyFields: ['id'],
      fields: {
        // Merge de dados financeiros
        financial: {
          merge: true
        },
        // Paginação para campanhas
        campaigns: {
          merge: false
        },
        // Paginação para marcas
        brands: {
          merge: false
        },
        // Merge de tags
        tags: {
          merge: (existing = [], incoming) => {
            return [...existing, ...incoming];
          }
        },
        // Merge de notes
        notes: {
          merge: (existing = [], incoming) => {
            return [...existing, ...incoming];
          }
        }
      }
    },

    // Configuração para InfluencerConnection (paginação)
    InfluencerConnection: {
      fields: {
        nodes: {
          merge: (existing = [], incoming) => {
            return [...existing, ...incoming];
          }
        }
      }
    },

    // Configuração para dados financeiros
    InfluencerFinancial: {
      keyFields: ['id'],
      fields: {
        prices: {
          merge: true
        },
        brandHistory: {
          merge: true
        }
      }
    },

    // Configuração para filtros salvos
    SavedFilter: {
      keyFields: ['id'],
      fields: {
        platforms: {
          merge: true
        }
      }
    },

    // Configuração para marcas
    Brand: {
      keyFields: ['id']
    },

    // Configuração para categorias
    Category: {
      keyFields: ['id']
    },

    // Configuração para estatísticas
    Stats: {
      keyFields: false // Stats são sempre únicos por usuário, não precisam de cache por ID
    },

    // 💰 Configuração para Budget - Cache otimizado
    Budget: {
      keyFields: ['id'],
      fields: {
        services: {
          merge: (existing = [], incoming) => {
            return [...existing, ...incoming];
          }
        },
        metadata: {
          merge: true
        }
      }
    },

    // 📋 Configuração para Proposal - Cache otimizado  
    Proposal: {
      keyFields: ['id'],
      fields: {
        budgets: {
          merge: (existing = [], incoming) => {
            return [...existing, ...incoming];
          }
        },
        collaborators: {
          merge: (existing = [], incoming) => {
            return [...existing, ...incoming];
          }
        },
        metadata: {
          merge: true
        }
      }
    },

    // 🔄 Configuração para ProposalBudgetsResult - Cache de orçamentos de propostas
    ProposalBudgetsResult: {
      keyFields: false, // Sempre único por consulta
      fields: {
        budgets: {
          merge: (existing = [], incoming) => {
            return incoming; // Sempre substitui com dados mais recentes
          }
        },
        influencerBudgets: {
          merge: (existing = [], incoming) => {
            return incoming; // Sempre substitui com dados mais recentes
          }
        },
        collaboratorCounterProposals: {
          merge: (existing = [], incoming) => {
            return incoming; // Sempre substitui com dados mais recentes
          }
        }
      }
    },

    // 💼 Configuração para CollaboratorCounterProposal - Cache de contrapropostas
    CollaboratorCounterProposal: {
      keyFields: ['id'],
      fields: {
        services: {
          merge: (existing = [], incoming) => {
            return [...existing, ...incoming];
          }
        },
        metadata: {
          merge: true
        }
      }
    },

    // Configuração para Query (cache de listas)
    Query: {
      fields: {
        influencers: {
          keyArgs: ['filters', 'userId'],
          merge: (existing, incoming, { args }) => {
            // Se é uma nova busca (offset = 0), substituir
            if (args?.pagination?.offset === 0) {
              return incoming;
            }
            
            // Se é paginação, concatenar
            if (existing && existing.nodes) {
              return {
                ...incoming,
                nodes: [...existing.nodes, ...incoming.nodes]
              };
            }
            
            return incoming;
          }
        },

        influencersByPriceRange: {
          keyArgs: ['priceRange', 'userId'],
          merge: (existing = [], incoming) => {
            return [...existing, ...incoming];
          }
        },

        influencersByPriceValues: {
          keyArgs: ['minPrice', 'maxPrice', 'userId'],
          merge: (existing = [], incoming) => {
            return [...existing, ...incoming];
          }
        },

        influencersByPlatform: {
          keyArgs: ['platform', 'userId'],
          merge: (existing = [], incoming) => {
            return [...existing, ...incoming];
          }
        },

        // Cache para categorias
        categories: {
          merge: false // Substituir sempre com dados mais recentes
        },

        // Cache para marcas
        brands: {
          keyArgs: ['userId'],
          merge: false // Substituir sempre com dados mais recentes
        },

        // Cache para estatísticas
        stats: {
          keyArgs: ['userId'],
          merge: false // Substituir sempre com dados mais recentes
        },

        // Cache para filtros salvos
        savedFilters: {
          keyArgs: ['userId'],
          merge: false // Substituir sempre com dados mais recentes
        }
      }
    }
  }
});

// Cliente Apollo principal - OTIMIZADO
export const apolloClient = new ApolloClient({
  link: from([
    errorLink,
    retryLink,
    authLink,
    httpLink
  ]),
  cache,
  defaultOptions: {
    watchQuery: {
      errorPolicy: 'all',
      fetchPolicy: 'cache-first', // 🔥 OTIMIZAÇÃO: Priorizar cache globalmente
      notifyOnNetworkStatusChange: false // 🔥 OTIMIZAÇÃO: Reduzir re-renders
    },
    query: {
      errorPolicy: 'all',
      fetchPolicy: 'cache-first' // 🔥 OTIMIZAÇÃO: Priorizar cache globalmente
    },
    mutate: {
      errorPolicy: 'all'
    }
  },
  connectToDevTools: process.env.NODE_ENV !== 'production'
});

// Hook para obter o cliente Apollo
export function useApolloClient() {
  return apolloClient;
}

// Função para limpar cache específico
export function clearInfluencerCache(influencerId?: string) {
  if (influencerId) {
    // Remover influenciador específico
    apolloClient.cache.evict({
      id: apolloClient.cache.identify({ __typename: 'Influencer', id: influencerId })
    });
  } else {
    // Limpar todo o cache de influenciadores
    apolloClient.cache.evict({ fieldName: 'influencers' });
    apolloClient.cache.evict({ fieldName: 'influencersByPriceRange' });
    apolloClient.cache.evict({ fieldName: 'influencersByPriceValues' });
    apolloClient.cache.evict({ fieldName: 'influencersByPlatform' });
  }
  
  apolloClient.cache.gc();
}

// Função para invalidar cache de dashboard
export function invalidateDashboardCache() {
  apolloClient.cache.evict({ fieldName: 'dashboardData' });
  apolloClient.cache.evict({ fieldName: 'financialStats' });
  apolloClient.cache.gc();
}

// Função para refetch queries específicas
export function refetchInfluencerQueries(userId: string) {
  apolloClient.refetchQueries({
    include: [
      'GetInfluencers',
      'GetDashboardData',
      'GetFinancialStats'
    ]
  });
}

// Função para limpar cache de filtros salvos
export function clearSavedFiltersCache(userId?: string) {
  if (userId) {
    apolloClient.cache.evict({ 
      fieldName: 'savedFilters',
      args: { userId }
    });
  } else {
    apolloClient.cache.evict({ fieldName: 'savedFilters' });
  }
  apolloClient.cache.gc();
}

// Função para limpar cache de marcas
export function clearBrandsCache(userId?: string) {
  if (userId) {
    apolloClient.cache.evict({ 
      fieldName: 'brands',
      args: { userId }
    });
  } else {
    apolloClient.cache.evict({ fieldName: 'brands' });
  }
  apolloClient.cache.gc();
}

// Função para limpar cache de categorias
export function clearCategoriesCache() {
  apolloClient.cache.evict({ fieldName: 'categories' });
  apolloClient.cache.gc();
}

// Função para limpar cache de estatísticas
export function clearStatsCache(userId?: string) {
  if (userId) {
    apolloClient.cache.evict({ 
      fieldName: 'stats',
      args: { userId }
    });
  } else {
    apolloClient.cache.evict({ fieldName: 'stats' });
  }
  apolloClient.cache.gc();
}

// Função para invalidar todos os caches relacionados
export function invalidateAllCaches(userId?: string) {
  clearInfluencerCache();
  clearSavedFiltersCache(userId);
  clearBrandsCache(userId);
  clearCategoriesCache();
  clearStatsCache(userId);
  invalidateDashboardCache();
}

export default apolloClient; 

